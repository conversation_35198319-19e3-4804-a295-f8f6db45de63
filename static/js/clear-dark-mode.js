/**
 * CLEAR Dark Mode System - JavaScript Controller
 * Handles theme switching, persistence, and HTMX integration
 *
 * Created: 2025-07-28
 * Task: #47 - Implement dark mode support throughout the application
 * Framework: HTMX 1.9.12 + Django 5.2.4 + Bootstrap 5.3.0
 */

(function() {
    'use strict';

    /**
     * CLEAR Theme Manager
     * Manages dark/light theme switching with persistence and HTMX integration
     */
    class ClearThemeManager {
        constructor() {
            this.STORAGE_KEY = 'clear-theme-preference';
            this.THEME_ATTRIBUTE = 'data-theme';
            this.THEMES = {
                LIGHT: 'light',
                DARK: 'dark',
                AUTO: 'auto'
            };

            this.currentTheme = this.THEMES.AUTO;
            this.systemPreference = this.getSystemPreference();
            this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

            this.init();
        }

        /**
         * Initialize the theme manager
         */
        init() {
            // Load saved preference or detect system preference
            this.loadThemePreference();

            // Apply initial theme
            this.applyTheme();

            // Listen for system preference changes
            this.mediaQuery.addEventListener('change', (e) => {
                this.systemPreference = e.matches ? this.THEMES.DARK : this.THEMES.LIGHT;
                if (this.currentTheme === this.THEMES.AUTO) {
                    this.applyTheme();
                }
            });

            // Initialize theme toggle components
            this.initializeToggleComponents();

            // HTMX integration
            this.initializeHTMXIntegration();

            // Announce theme to screen readers
            this.announceThemeChange();

            console.log('CLEAR Theme Manager initialized:', {
                currentTheme: this.currentTheme,
                systemPreference: this.systemPreference,
                appliedTheme: this.getAppliedTheme()
            });
        }

        /**
         * Get system color scheme preference
         */
        getSystemPreference() {
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                return this.THEMES.DARK;
            }
            return this.THEMES.LIGHT;
        }

        /**
         * Load theme preference from localStorage
         */
        loadThemePreference() {
            try {
                const saved = localStorage.getItem(this.STORAGE_KEY);
                if (saved && Object.values(this.THEMES).includes(saved)) {
                    this.currentTheme = saved;
                } else {
                    this.currentTheme = this.THEMES.AUTO;
                }
            } catch (error) {
                console.warn('Failed to load theme preference:', error);
                this.currentTheme = this.THEMES.AUTO;
            }
        }

        /**
         * Save theme preference to localStorage
         */
        saveThemePreference() {
            try {
                localStorage.setItem(this.STORAGE_KEY, this.currentTheme);
            } catch (error) {
                console.warn('Failed to save theme preference:', error);
            }
        }

        /**
         * Get the theme that should be applied (resolving 'auto')
         */
        getAppliedTheme() {
            if (this.currentTheme === this.THEMES.AUTO) {
                return this.systemPreference;
            }
            return this.currentTheme;
        }

        /**
         * Apply the current theme to the document
         */
        applyTheme() {
            const appliedTheme = this.getAppliedTheme();
            const root = document.documentElement;

            // Remove existing theme attributes
            root.removeAttribute(this.THEME_ATTRIBUTE);

            // Apply new theme
            if (appliedTheme === this.THEMES.DARK) {
                root.setAttribute(this.THEME_ATTRIBUTE, this.THEMES.DARK);
            }
            // Light theme is default (no attribute needed)

            // Update toggle components
            this.updateToggleComponents();

            // Dispatch theme change event
            this.dispatchThemeChangeEvent(appliedTheme);

            // Update meta theme-color for mobile browsers
            this.updateMetaThemeColor(appliedTheme);

            // Announce to screen readers
            this.announceThemeChange();
        }

        /**
         * Set theme preference
         */
        setTheme(theme) {
            if (!Object.values(this.THEMES).includes(theme)) {
                console.warn('Invalid theme:', theme);
                return;
            }

            this.currentTheme = theme;
            this.saveThemePreference();
            this.applyTheme();
        }

        /**
         * Toggle between light and dark themes
         */
        toggleTheme() {
            const appliedTheme = this.getAppliedTheme();
            const newTheme = appliedTheme === this.THEMES.DARK ? this.THEMES.LIGHT : this.THEMES.DARK;
            this.setTheme(newTheme);
        }

        /**
         * Cycle through all theme options (light -> dark -> auto)
         */
        cycleTheme() {
            const themeOrder = [this.THEMES.LIGHT, this.THEMES.DARK, this.THEMES.AUTO];
            const currentIndex = themeOrder.indexOf(this.currentTheme);
            const nextIndex = (currentIndex + 1) % themeOrder.length;
            this.setTheme(themeOrder[nextIndex]);
        }

        /**
         * Initialize theme toggle components
         */
        initializeToggleComponents() {
            // Find all theme toggle elements
            const toggles = document.querySelectorAll('[data-clear-theme-toggle]');

            toggles.forEach(toggle => {
                // Remove existing listeners to prevent duplicates
                toggle.removeEventListener('click', this.handleToggleClick);

                // Add click listener
                toggle.addEventListener('click', (e) => this.handleToggleClick(e));

                // Add keyboard support
                toggle.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.handleToggleClick(e);
                    }
                });

                // Ensure toggle is focusable
                if (!toggle.hasAttribute('tabindex')) {
                    toggle.setAttribute('tabindex', '0');
                }

                // Add ARIA attributes
                toggle.setAttribute('role', 'button');
                toggle.setAttribute('aria-label', this.getToggleAriaLabel());
            });

            // Update initial state
            this.updateToggleComponents();
        }

        /**
         * Handle theme toggle click
         */
        handleToggleClick(event) {
            event.preventDefault();

            const toggle = event.currentTarget;
            const action = toggle.getAttribute('data-clear-theme-toggle');

            switch (action) {
                case 'toggle':
                    this.toggleTheme();
                    break;
                case 'cycle':
                    this.cycleTheme();
                    break;
                case 'light':
                    this.setTheme(this.THEMES.LIGHT);
                    break;
                case 'dark':
                    this.setTheme(this.THEMES.DARK);
                    break;
                case 'auto':
                    this.setTheme(this.THEMES.AUTO);
                    break;
                default:
                    this.toggleTheme();
            }
        }

        /**
         * Update theme toggle components
         */
        updateToggleComponents() {
            const toggles = document.querySelectorAll('[data-clear-theme-toggle]');
            const appliedTheme = this.getAppliedTheme();

            toggles.forEach(toggle => {
                // Update ARIA label
                toggle.setAttribute('aria-label', this.getToggleAriaLabel());

                // Update text content if it has a text element
                const textElement = toggle.querySelector('.clear-theme-toggle-text');
                if (textElement) {
                    textElement.textContent = this.getThemeDisplayName();
                }

                // Update icon rotation for dark theme
                const iconElement = toggle.querySelector('.clear-theme-toggle-icon');
                if (iconElement) {
                    if (appliedTheme === this.THEMES.DARK) {
                        iconElement.style.transform = 'rotate(180deg)';
                    } else {
                        iconElement.style.transform = 'rotate(0deg)';
                    }
                }

                // Update active state classes
                toggle.classList.toggle('active', appliedTheme === this.THEMES.DARK);
                toggle.classList.toggle('clear-theme-dark', appliedTheme === this.THEMES.DARK);
                toggle.classList.toggle('clear-theme-light', appliedTheme === this.THEMES.LIGHT);
            });
        }

        /**
         * Get ARIA label for theme toggle
         */
        getToggleAriaLabel() {
            const appliedTheme = this.getAppliedTheme();
            const nextTheme = appliedTheme === this.THEMES.DARK ? 'light' : 'dark';
            return `Switch to ${nextTheme} theme. Current theme: ${this.getThemeDisplayName()}`;
        }

        /**
         * Get display name for current theme
         */
        getThemeDisplayName() {
            if (this.currentTheme === this.THEMES.AUTO) {
                return `Auto (${this.systemPreference})`;
            }
            return this.currentTheme.charAt(0).toUpperCase() + this.currentTheme.slice(1);
        }

        /**
         * Dispatch theme change event
         */
        dispatchThemeChangeEvent(appliedTheme) {
            const event = new CustomEvent('clear:theme-changed', {
                detail: {
                    theme: appliedTheme,
                    preference: this.currentTheme,
                    systemPreference: this.systemPreference
                },
                bubbles: true
            });

            document.dispatchEvent(event);
        }

        /**
         * Update meta theme-color for mobile browsers
         */
        updateMetaThemeColor(appliedTheme) {
            let metaThemeColor = document.querySelector('meta[name="theme-color"]');

            if (!metaThemeColor) {
                metaThemeColor = document.createElement('meta');
                metaThemeColor.name = 'theme-color';
                document.head.appendChild(metaThemeColor);
            }

            // Use CSS custom property values
            const color = appliedTheme === this.THEMES.DARK ? '#1a1a1a' : '#ffffff';
            metaThemeColor.content = color;
        }

        /**
         * Announce theme change to screen readers
         */
        announceThemeChange() {
            // Create or update live region for screen reader announcements
            let liveRegion = document.getElementById('clear-theme-announcement');

            if (!liveRegion) {
                liveRegion = document.createElement('div');
                liveRegion.id = 'clear-theme-announcement';
                liveRegion.setAttribute('aria-live', 'polite');
                liveRegion.setAttribute('aria-atomic', 'true');
                liveRegion.style.position = 'absolute';
                liveRegion.style.left = '-10000px';
                liveRegion.style.width = '1px';
                liveRegion.style.height = '1px';
                liveRegion.style.overflow = 'hidden';
                document.body.appendChild(liveRegion);
            }

            // Announce theme change
            const appliedTheme = this.getAppliedTheme();
            liveRegion.textContent = `Theme changed to ${appliedTheme} mode`;
        }

        /**
         * Initialize HTMX integration
         */
        initializeHTMXIntegration() {
            // Listen for HTMX events to reinitialize toggles after content updates
            document.addEventListener('htmx:afterSwap', () => {
                this.initializeToggleComponents();
            });

            document.addEventListener('htmx:afterSettle', () => {
                this.initializeToggleComponents();
            });

            // Add theme information to HTMX requests
            document.addEventListener('htmx:configRequest', (event) => {
                event.detail.headers['X-Clear-Theme'] = this.getAppliedTheme();
                event.detail.headers['X-Clear-Theme-Preference'] = this.currentTheme;
            });

            // Handle theme-specific HTMX responses
            document.addEventListener('htmx:beforeSwap', (event) => {
                const response = event.detail.xhr;
                const themeHeader = response.getResponseHeader('X-Clear-Theme-Required');

                if (themeHeader && themeHeader !== this.getAppliedTheme()) {
                    // Server suggests a different theme
                    console.log('Server suggested theme change:', themeHeader);
                }
            });
        }

        /**
         * Get current theme information
         */
        getThemeInfo() {
            return {
                current: this.currentTheme,
                applied: this.getAppliedTheme(),
                system: this.systemPreference,
                displayName: this.getThemeDisplayName()
            };
        }
    }

    /**
     * Theme Toggle Component Factory
     * Creates theme toggle elements programmatically
     */
    class ClearThemeToggleFactory {
        /**
         * Create a theme toggle button
         */
        static createToggleButton(options = {}) {
            const {
                variant = 'button', // 'button' or 'inline'
                action = 'toggle',   // 'toggle', 'cycle', 'light', 'dark', 'auto'
                showText = false,
                className = '',
                iconHtml = '🌓'
            } = options;

            const button = document.createElement('button');
            button.className = `clear-theme-toggle${variant === 'button' ? '-btn' : ''} ${className}`.trim();
            button.setAttribute('data-clear-theme-toggle', action);
            button.type = 'button';

            // Create icon element
            const icon = document.createElement('span');
            icon.className = 'clear-theme-toggle-icon';
            icon.innerHTML = iconHtml;
            button.appendChild(icon);

            // Add text if requested
            if (showText) {
                const text = document.createElement('span');
                text.className = 'clear-theme-toggle-text';
                text.textContent = 'Theme';
                button.appendChild(text);
            }

            return button;
        }

        /**
         * Create a theme selection dropdown
         */
        static createThemeSelector(options = {}) {
            const {
                className = '',
                includeAuto = true
            } = options;

            const select = document.createElement('select');
            select.className = `form-select clear-theme-selector ${className}`.trim();
            select.setAttribute('data-clear-theme-selector', 'true');

            // Add options
            const lightOption = document.createElement('option');
            lightOption.value = 'light';
            lightOption.textContent = 'Light Theme';
            select.appendChild(lightOption);

            const darkOption = document.createElement('option');
            darkOption.value = 'dark';
            darkOption.textContent = 'Dark Theme';
            select.appendChild(darkOption);

            if (includeAuto) {
                const autoOption = document.createElement('option');
                autoOption.value = 'auto';
                autoOption.textContent = 'Auto (System)';
                select.appendChild(autoOption);
            }

            // Add change listener
            select.addEventListener('change', (e) => {
                if (window.clearThemeManager) {
                    window.clearThemeManager.setTheme(e.target.value);
                }
            });

            return select;
        }
    }

    /**
     * Initialize when DOM is ready
     */
    function initializeClearThemeSystem() {
        // Initialize theme manager
        window.clearThemeManager = new ClearThemeManager();

        // Make factory available globally
        window.ClearThemeToggleFactory = ClearThemeToggleFactory;

        // Add utility functions to window
        window.clearTheme = {
            get: () => window.clearThemeManager.getThemeInfo(),
            set: (theme) => window.clearThemeManager.setTheme(theme),
            toggle: () => window.clearThemeManager.toggleTheme(),
            cycle: () => window.clearThemeManager.cycleTheme()
        };

        console.log('CLEAR Theme System initialized');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeClearThemeSystem);
    } else {
        initializeClearThemeSystem();
    }

    // Handle page visibility changes to sync with system preference
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden && window.clearThemeManager) {
            const newSystemPreference = window.clearThemeManager.getSystemPreference();
            if (newSystemPreference !== window.clearThemeManager.systemPreference) {
                window.clearThemeManager.systemPreference = newSystemPreference;
                if (window.clearThemeManager.currentTheme === window.clearThemeManager.THEMES.AUTO) {
                    window.clearThemeManager.applyTheme();
                }
            }
        }
    });

})();
